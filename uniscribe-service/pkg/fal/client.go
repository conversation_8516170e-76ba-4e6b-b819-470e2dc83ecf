package fal

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"uniscribe-service/internal/config"
)

const (
	baseURL = "https://queue.fal.run"
)

type Client struct {
	httpClient *http.Client
	apiKey     string
}

type RequestStatus struct {
	Status      string         `json:"status"`
	RequestID   string         `json:"request_id"`
	ResponseURL string         `json:"response_url,omitempty"`
	StatusURL   string         `json:"status_url,omitempty"`
	CancelURL   string         `json:"cancel_url,omitempty"`
	Logs        []LogEntry     `json:"logs,omitempty"`
	Metrics     map[string]any `json:"metrics,omitempty"`
}

type LogEntry struct {
	Timestamp string         `json:"timestamp"`
	Message   string         `json:"message"`
	Labels    map[string]any `json:"labels"`
}

type StreamStatus struct {
	Status      string         `json:"status"`
	RequestID   string         `json:"request_id"`
	ResponseURL string         `json:"response_url"`
	StatusURL   string         `json:"status_url"`
	CancelURL   string         `json:"cancel_url"`
	Logs        []LogEntry     `json:"logs"`
	Metrics     map[string]any `json:"metrics"`
}

type QueueResponse struct {
	RequestID string `json:"request_id"`
}

// NewClient creates a new Fal API client
func NewClient(apiKey string) *Client {
	return &Client{
		httpClient: &http.Client{
			Timeout: time.Second * 60 * 10,
		},
		apiKey: apiKey,
	}
}

// GetClient returns a singleton instance of the Fal client
func GetClient() *Client {
	return NewClient(config.Cfg.FalApiKey)
}

// QueueRequest submits a request to a specific Fal app
func (c *Client) QueueRequest(appID string, payload map[string]interface{}) (*QueueResponse, error) {
	url := fmt.Sprintf("%s/%s", baseURL, appID)

	body, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request payload: %w", err)
	}

	req, err := http.NewRequest(http.MethodPost, url, bytes.NewBuffer(body))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(req)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	var result QueueResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

// GetRequestStatus checks the status of a specific request
func (c *Client) GetRequestStatus(appID, requestID string) (*RequestStatus, error) {
	url := fmt.Sprintf("%s/%s/requests/%s/status", baseURL, appID, requestID)

	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(req)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	var status RequestStatus
	if err := json.NewDecoder(resp.Body).Decode(&status); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &status, nil
}

// GetRequestResult retrieves the raw JSON response of a completed request
func (c *Client) GetRequestResult(appID, requestID string) ([]byte, error) {
	url := fmt.Sprintf("%s/%s/requests/%s", baseURL, appID, requestID)

	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(req)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("failed to transcribe: status=%d, body=%s", resp.StatusCode, string(body))
	}

	defer resp.Body.Close()

	return io.ReadAll(resp.Body)
}

// CancelRequest attempts to cancel a queued request
func (c *Client) CancelRequest(appID, requestID string) error {
	url := fmt.Sprintf("%s/%s/requests/%s/cancel", baseURL, appID, requestID)

	req, err := http.NewRequest(http.MethodPost, url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	c.setHeaders(req)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to cancel request: status=%d, body=%s", resp.StatusCode, string(body))
	}

	return nil
}

// setHeaders adds the necessary headers to the request
func (c *Client) setHeaders(req *http.Request) {
	req.Header.Set("Authorization", fmt.Sprintf("Key %s", c.apiKey))
	req.Header.Set("Content-Type", "application/json")
}

// WaitForRequestCompletion 等待请求完成并返回最终状态
func (c *Client) WaitForRequestCompletion(ctx context.Context, appID, requestID string, withLogs bool) (*StreamStatus, error) {
	url := fmt.Sprintf("%s/%s/requests/%s/status/stream", baseURL, appID, requestID)
	if withLogs {
		url += "?logs=1"
	}

	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req = req.WithContext(ctx) // 使用传入的上下文，支持取消
	c.setHeaders(req)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	reader := bufio.NewReader(resp.Body)
	var lastStatus *StreamStatus

	for {
		// 添加延迟，避免过于频繁地读取流
		// 考虑到转录任务通常需要较长时间（几秒到几分钟），使用较大的延迟值
		time.Sleep(1 * time.Second)

		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
			// 继续执行
		}

		line, err := reader.ReadString('\n')
		if err != nil {
			if err != io.EOF {
				return nil, fmt.Errorf("failed to read stream: %w", err)
			} else {
				// 流结束，但没有收到完成状态
				if lastStatus != nil {
					return lastStatus, nil
				}
				return nil, fmt.Errorf("stream ended without completion")
			}
		}

		line = strings.TrimSpace(line)
		if line == "" || line == ": ping" {
			continue
		}

		if !strings.HasPrefix(line, "data: ") {
			log.Printf("[WARN] Fal: Unexpected line format for requestID=%s: %s", requestID, line)
			continue
		}

		data := strings.TrimPrefix(line, "data: ")
		var status StreamStatus
		if err := json.Unmarshal([]byte(data), &status); err != nil {
			return nil, fmt.Errorf("failed to decode status: %w", err)
		}

		lastStatus = &status

		// 记录转录进度日志
		for _, logEntry := range status.Logs {
			log.Printf("Fal log: %s - %s\n", logEntry.Timestamp, logEntry.Message)
		}
		if status.Status == "COMPLETED" {
			log.Printf("[INFO] Fal: Task completed for requestID=%s", requestID)
			return &status, nil
		}
	}
}
