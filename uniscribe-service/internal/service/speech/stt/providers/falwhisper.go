package providers

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"uniscribe-service/pkg/fal"
)

const falWhisperAppID = "fal-ai/whisper"

type FalWhisperProvider struct {
	client *fal.Client
}

type FalWhisperChunk struct {
	Timestamp []float64 `json:"timestamp"`
	Text      string    `json:"text"`
	Speaker   string    `json:"speaker,omitempty"`
}

type FalWhisperResult struct {
	Text                string               `json:"text"`
	Chunks              []FalWhisperChunk    `json:"chunks"`
	InferredLanguages   []string             `json:"inferred_languages"`
	DiarizationSegments []DiarizationSegment `json:"diarization_segments,omitempty"`
}

type DiarizationSegment struct {
	Timestamp []float64 `json:"timestamp"`
	Speaker   string    `json:"speaker"`
}

func NewFalWhisperProvider() *FalWhisperProvider {
	return &FalWhisperProvider{
		client: fal.GetClient(),
	}
}

func (f *FalWhisperProvider) Name() string {
	return string(FalWhisper)
}

func (f *FalWhisperProvider) Transcribe(ctx context.Context, task Task) (TranscriptionResult, error) {
	payload := map[string]interface{}{
		"audio_url":   task.FileUrl,
		"task":        "transcribe",
		"diarize":     false, // 当前有问题，不要使用 true
		"chunk_level": "segment",
		"version":     "3",
		"batch_size":  64,
	}

	// 只有在 LanguageCode 不为空时才添加 language 字段
	if task.LanguageCode != "" {
		if task.LanguageCode == "zh_tw" {
			payload["language"] = "zh"
		} else {
			payload["language"] = task.LanguageCode
		}
	}

	// Submit request using the generic client
	resp, err := f.client.QueueRequest(falWhisperAppID, payload)
	if err != nil {
		return TranscriptionResult{}, fmt.Errorf("failed to submit transcription request: %w", err)
	}

	result, err := f.pollForResult(ctx, resp.RequestID)
	if err != nil {
		return TranscriptionResult{}, fmt.Errorf("failed to get transcription result: %w", err)
	}

	segments := f.convertToUnifiedSegments(result)

	return TranscriptionResult{
		Text:             result.Text,
		Segments:         segments,
		DetectedLanguage: strings.Join(result.InferredLanguages, ","),
	}, nil
}

func (f *FalWhisperProvider) pollForResult(ctx context.Context, requestID string) (*FalWhisperResult, error) {
	// 使用新的阻塞式方法等待请求完成
	_, err := f.client.WaitForRequestCompletion(ctx, falWhisperAppID, requestID, false)
	if err != nil {
		log.Printf("[ERROR] FalWhisper: Error waiting for completion for requestID=%s: %v", requestID, err)
		return nil, fmt.Errorf("error waiting for completion: %w", err)
	}

	// 请求已完成，获取结果
	log.Printf("[INFO] FalWhisper: Stream completed for requestID=%s, fetching result", requestID)
	resultBytes, err := f.client.GetRequestResult(falWhisperAppID, requestID)
	if err != nil {
		log.Printf("[ERROR] FalWhisper: Failed to get result for requestID=%s: %v", requestID, err)
		return nil, fmt.Errorf("failed to get result: %w", err)
	}

	var result FalWhisperResult
	if err := json.Unmarshal(resultBytes, &result); err != nil {
		log.Printf("[ERROR] FalWhisper: Failed to unmarshal result for requestID=%s: %v", requestID, err)
		return nil, fmt.Errorf("failed to unmarshal result: %w", err)
	}

	log.Printf("[INFO] FalWhisper: Successfully got result for requestID=%s: %+v", requestID, result)
	return &result, nil
}

func (f *FalWhisperProvider) convertToUnifiedSegments(result *FalWhisperResult) []UnifiedSegment {
	segments := make([]UnifiedSegment, len(result.Chunks))
	for i, chunk := range result.Chunks {
		segments[i] = UnifiedSegment{
			ID:        i,
			StartTime: chunk.Timestamp[0],
			EndTime:   chunk.Timestamp[1],
			Text:      chunk.Text,
		}
	}
	return segments
}
