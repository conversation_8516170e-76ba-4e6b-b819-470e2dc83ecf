from flask_restful import Resource
from flask import g
from constants.task import TaskType
from controllers.task import (
    get_next_task,
    save_task_result,
    update_file_status,
    create_transcription_task,
    create_text_tasks,
)
from libs import reqparse
from models.task import Task
from models.task_result import TaskResult
from resources.auth import auth_required

# 无论是 提供给前端还是 go 服务，统一用驼峰风格命名字段


# 这个接口提供给前端，用于创建任务
class CreateTranscriptionTaskResource(Resource):
    @auth_required
    def post(self):
        user_id = g.user.id
        parser = reqparse.RequestParser()
        transcription_file_id = parser.get_argument(
            "transcriptionFileId", type=int, required=True, location="json"
        )
        task = create_transcription_task(user_id, transcription_file_id)
        return {"data": {"taskId": task.id, "status": task.status}}


# 这个接口提供给 go 服务，用于创建文本处理任务
class CreateTextTasksResource(Resource):
    def post(self):
        # TODO： API 鉴权
        parser = reqparse.RequestParser()
        transcription_file_id = parser.get_argument(
            "transcriptionFileId", type=int, required=True, location="json"
        )
        tasks = create_text_tasks(transcription_file_id)
        return {"data": [task.id for task in tasks]}


class TaskNextResource(Resource):
    def post(self):
        # Note: 由于不是幂等的，不能用 GET，用 POST 代替
        # TODO：API 鉴权
        next_task = get_next_task()
        update_file_status(next_task.file_id)
        task_result = TaskResult.get_by_file_id(next_task.file_id)
        transcription_text = task_result.original_text if task_result else ""
        response = {
            "taskId": next_task.id,
            "fileUrl": next_task.file_url,
            "transcriptionFileId": next_task.file_id,
            "taskType": next_task.task_type,  # 不用转换为字符串
            "transcriptionText": transcription_text,
            "fileDuration": next_task.file_duration,
            "languageCode": next_task.language_code,
            "language": next_task.language,
            "transcriptionType": next_task.transcription_type,
        }

        # 如果有请求的服务提供商，添加到响应中
        if next_task.requested_service_provider:
            response["requestedServiceProvider"] = next_task.requested_service_provider

        return response


class TaskResultResource(Resource):
    def post(self):
        # TODO：API 鉴权
        parser = reqparse.RequestParser()
        task_id = parser.get_argument(
            "taskId", type=int, required=True, location="json"
        )
        task_type = parser.get_argument(
            "taskType", type=int, required=True, location="json"
        )

        error_message = parser.get_argument(
            "errorMessage", type=str, location="json", required=False
        )
        if error_message:
            save_task_result(task_id, task_type, error_message=error_message)
        else:
            if task_type == TaskType.transcription.id:
                original_text = parser.get_argument(
                    "transcriptionText", type=str, location="json", required=True
                )
                segments = parser.get_argument(
                    "segments", type=list, location="json", required=True
                )
                detected_language = parser.get_argument(
                    "detectedLanguage", type=str, location="json", required=False
                )
                service_provider = parser.get_argument(
                    "serviceProvider", type=str, location="json", required=False
                )
                save_task_result(
                    task_id,
                    task_type,
                    original_text=original_text,
                    segments=segments,
                    detected_language=detected_language,
                    service_provider=service_provider,
                )
            elif task_type == TaskType.correction.id:
                corrected_text = parser.get_argument(
                    "correctedText", type=str, location="json", required=True
                )
                save_task_result(task_id, task_type, corrected_text=corrected_text)
            elif task_type == TaskType.summary.id:
                summary = parser.get_argument(
                    "summary", type=str, location="json", required=True
                )
                save_task_result(task_id, task_type, summary=summary)
            elif task_type == TaskType.keywords.id:
                keywords = parser.get_argument(
                    "keywords", type=list, location="json", required=True
                )
                save_task_result(task_id, task_type, keywords=keywords)
            elif task_type == TaskType.outline.id:
                outline = parser.get_argument(
                    "outline", type=str, location="json", required=True
                )
                save_task_result(task_id, task_type, outline=outline)
            elif task_type == TaskType.translation.id:
                # TODO
                pass
            elif task_type == TaskType.qa_extraction.id:
                qa_extraction = parser.get_argument(
                    "qaExtraction", type=list, location="json", required=True
                )
                save_task_result(task_id, task_type, qa_extraction=qa_extraction)
            else:
                raise NotImplemented

        task = Task.get_by_id(task_id)
        update_file_status(file_id=task.file_id)
        return {"message": "success"}
