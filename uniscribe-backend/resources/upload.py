import base64
import logging
import mimetypes
from flask import g
from flask_restful import Resource, abort, marshal_with
from flask import current_app

from constants.storage import EXPIRE_FOR_UPLOAD_URL
from constants.task import TranscriptionType
from controllers.transcription import (
    create_transcription_file,
    complete_upload,
    format_transcription_file,
    check_free_user_transcribe_limit,
)
from exceptions.storage import FileAlreadyExistsError
from fields.transcription import transcription_file_fields
from libs import reqparse
from models.transcription_file import TranscriptionFile
from resources.auth import auth_required
from controllers.usage import check_transcription_quota


logger = logging.getLogger(__name__)


class GenerateSignedUrlResource(Resource):
    @auth_required
    def post(self):
        user_id = g.user.id
        parser = reqparse.RequestParser()
        filename = parser.get_argument(
            "filename", type=str, required=True, location="json"
        )
        file_type = parser.get_argument(
            "fileType", type=str, required=True, location="json"
        )
        file_size = parser.get_argument(
            "fileSize", type=int, required=True, location="json"
        )
        content_md5_base_64 = parser.get_argument(
            "contentMd5Base64", type=str, required=True, location="json"
        )
        duration = parser.get_argument(
            "duration", type=float, required=True, location="json"
        )

        force_upload = parser.get_argument(
            "forceUpload", type=bool, required=False, location="json", default=False
        )
        language_code = parser.get_argument(
            "languageCode", type=str, required=False, location="json", default=None
        )
        transcription_type = parser.get_argument(
            "transcriptionType",
            type=str,
            required=False,
            location="json",
            default=TranscriptionType.TRANSCRIPT.value,
        )

        # 检查免费用户上传限制
        check_free_user_transcribe_limit(g.user)

        # 先检查配额
        check_transcription_quota(user_id, duration)

        try:
            md5_hash = base64.b64decode(content_md5_base_64)
            fingerprint = md5_hash.hex()
        except Exception as e:
            logger.error(f"base64 decode error: {e}")
            abort(400, message="Invalid contentMd5Base64")

        # 检查文件是否存在
        if not force_upload:
            tf = TranscriptionFile.get_by_user_id_and_fingerprint(user_id, fingerprint)
            if tf:
                raise FileAlreadyExistsError(
                    message=f"File {filename} already exists",
                    details={
                        "fingerprint": fingerprint,
                        "fileName": filename,
                        "createdTime": tf.created_time,
                    },
                )

        # 配额充足才创建文件
        tf = create_transcription_file(
            user_id,
            filename,
            file_type,
            file_size,
            fingerprint,
            duration,
            language_code,
            transcription_type,
        )

        # guess content-type from file-type, use mime
        content_type = mimetypes.guess_type(file_type)[0]

        storage = current_app.storage
        presigned_url = storage.generate_presigned_url_for_upload(
            tf.file_key,
            EXPIRE_FOR_UPLOAD_URL,
            content_md5_base_64=content_md5_base_64,
            content_type=content_type,
        )

        return {
            "transcriptionFileId": str(tf.id),
            "preSignedUrl": presigned_url,
        }


class CompleteUploadResource(Resource):
    @auth_required
    @marshal_with(transcription_file_fields)
    def post(self):
        user_id = g.user.id
        parser = reqparse.RequestParser()
        transcription_file_id = parser.get_argument(
            "transcriptionFileId", type=str, required=True, location="json"
        )
        transcription_file = complete_upload(user_id, transcription_file_id)
        transcription_file = format_transcription_file(transcription_file)
        return transcription_file
