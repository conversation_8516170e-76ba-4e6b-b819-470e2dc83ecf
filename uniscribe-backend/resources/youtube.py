import logging
import re
from flask import current_app, g
from flask_restful import Resource, abort, marshal_with

from constants.youtube_transcript import AVAILABLE_FORMATS
from constants.task import TranscriptionType
from constants.transcription import TranscriptionFileStatus
from constants.storage import EXPIRE_FOR_READ_URL
from exceptions.youtube import (
    YoutubeSubtitleNotFoundError,
    YoutubeSubtitleLiveChatOnlyError,
)
from libs.reqparse import RequestParser
from libs.cache import RedisCache
from resources.auth import auth_required
from controllers.transcription import format_transcription_file
from controllers.youtube import (
    YoutubeInfoExtractor,
    YoutubeTranscriber,
    YoutubeTranscriptExtractor,
    YoutubeTranscriptManager,
    YoutubeTranscript,
)
from controllers.task import create_transcription_task
from controllers.export import create_download_response
from fields.transcription import transcription_file_fields


logger = logging.getLogger(__name__)


def youtube_url_validator(url: str):
    return re.match(
        r"^(https?://)?(www\.)?(youtube\.com/shorts/.*|youtube\.com/watch\?v=.*|youtu\.be/[a-zA-Z0-9_-]+(\?.*)?)",
        url,
    )


class YoutubeInfoResource(Resource):
    def __init__(self):
        self.cache = RedisCache(prefix="youtube_info:v1")

    def post(self):
        """处理下载请求"""
        parser = RequestParser()
        url = parser.get_argument("url", type=str, required=True, location="json")

        if not youtube_url_validator(url):
            abort(400, message="Invalid YouTube URL")

        return self.get_cached_info(url)

    def get_cached_info(self, url: str):
        # 尝试获取缓存
        cached_data = self.cache.get(url)
        if cached_data is not None:
            return cached_data

        # 无缓存时获取新数据
        extractor = YoutubeInfoExtractor(url)
        result = extractor.extract()

        # 设置缓存，24小时过期
        self.cache.set(url, result, ttl=3600 * 24)
        return result


class CreateYoutubeTranscriptionTaskResource(Resource):
    @auth_required
    @marshal_with(transcription_file_fields)
    def post(self):
        """处理音频转录请求"""
        parser = RequestParser()
        url = parser.get_argument("url", type=str, required=True, location="json")
        title = parser.get_argument("title", type=str, required=True, location="json")
        duration = parser.get_argument(
            "duration", type=int, required=True, location="json"
        )
        transcription_type = parser.get_argument(
            "transcriptionType",
            type=str,
            required=False,
            location="json",
            default=TranscriptionType.TRANSCRIPT.value,
        )
        language_code = parser.get_argument(
            "languageCode", type=str, required=False, location="json", default=None
        )

        if not youtube_url_validator(url):
            abort(400, message="Invalid YouTube URL")

        storage = current_app.storage
        app = current_app._get_current_object()
        transcriber = YoutubeTranscriber(g.user.id, url, title, duration, storage)
        tf = transcriber.create_transcription_file(transcription_type, language_code)

        def process_transcription():
            with app.app_context():
                try:
                    return transcriber.transcribe()
                except Exception as e:
                    logger.error(f"Transcription failed: {str(e)}")
                    raise
                finally:
                    transcriber.cleanup()

        # 在提交异步任务之前，先准备好响应数据
        storage = current_app.storage
        tf.file_url = storage.generate_presigned_url_for_read(
            tf.file_key,
            EXPIRE_FOR_READ_URL,
        )
        tf.status = TranscriptionFileStatus.by_id(tf.status).name

        # 提交异步任务
        task_id = current_app.task_manager.submit_task(process_transcription)
        logger.info(f"Youtube download task {task_id} submitted for transcription")

        # 返回已准备好的响应数据，避免在异步任务启动后再访问数据库
        return tf


class YoutubeTranscriptionTaskStatusResource(Resource):
    @auth_required
    def get(self, task_id):
        """获取任务状态"""
        status = current_app.task_manager.get_task_status(task_id)
        if not status:
            abort(404, message="Task not found")
        return status


class YoutubeSubtitleListResource(Resource):
    """获取视频的所有字幕并保存到数据库"""

    def post(self):
        parser = RequestParser()
        url = parser.get_argument("url", type=str, required=True, location="json")

        if not youtube_url_validator(url):
            abort(400, message="Invalid YouTube URL")

        video_id = YoutubeTranscript.extract_video_id(url)
        if not video_id:
            abort(400, message="Invalid YouTube URL")

        # 1. 先查询数据库中已有的字幕
        existing_transcripts = YoutubeTranscript.query.filter_by(
            video_id=video_id
        ).all()

        if existing_transcripts:
            # 如果数据库中有字幕，直接返回
            return {
                "video_id": video_id,
                "subtitles": [
                    {
                        "langCode": t.lang_code,
                        "name": t.lang_name,
                        "vttContent": t.vtt_content,
                        "availableFormats": AVAILABLE_FORMATS,
                        "isAuto": t.is_auto,
                    }
                    for t in existing_transcripts
                ],
            }

        # 2. 如果数据库中没有，则从 YouTube 获取
        extractor = YoutubeTranscriptExtractor(url)
        available_subtitles = extractor.list_subtitles()

        subtitles = available_subtitles["subtitles"]

        # 过滤掉 live_chat，只保留真正的字幕
        real_subtitles = [s for s in subtitles if s["lang_code"] != "live_chat"]

        if not real_subtitles:
            # 如果过滤后没有真正的字幕
            if any(s["lang_code"] == "live_chat" for s in subtitles):
                # 但原始列表中有 live_chat
                raise YoutubeSubtitleLiveChatOnlyError(
                    message="This video only has live chat records, no subtitles are available.",
                    details={
                        "video_id": video_id,
                        "url": url,
                    },
                )
            else:
                # 完全没有任何字幕
                raise YoutubeSubtitleNotFoundError(
                    message="The video does not have any subtitles.",
                    details={
                        "video_id": video_id,
                        "url": url,
                    },
                )

        saved_subtitles = []
        for subtitle in real_subtitles:
            try:
                vtt_content = extractor.download_subtitle(subtitle["lang_code"])
                transcript = YoutubeTranscriptManager.save_transcript(
                    url,
                    subtitle["lang_code"],
                    subtitle["name"],
                    vtt_content,
                    subtitle["is_auto"],
                )
                saved_subtitles.append(
                    {
                        "langCode": transcript.lang_code,
                        "name": transcript.lang_name,
                        "vttContent": vtt_content,
                        "availableFormats": AVAILABLE_FORMATS,
                        "isAuto": transcript.is_auto,
                    }
                )
            except Exception as e:
                logger.error(
                    f"Failed to save subtitle {subtitle['lang_code']}: {str(e)}"
                )
                continue

        if not saved_subtitles:
            abort(
                404,
                message="Failed to save any subtitles",
            )

        return {"video_id": video_id, "subtitles": saved_subtitles}


class YoutubeSubtitleDownloadResource(Resource):
    """下载指定格式的字幕文件"""

    def post(self):
        parser = RequestParser()
        video_id = parser.get_argument(
            "videoId", type=str, required=True, location="json"
        )
        lang_code = parser.get_argument(
            "langCode", type=str, required=True, location="json"
        )
        format = parser.get_argument(
            "format",
            type=str,
            default="vtt",
            choices=AVAILABLE_FORMATS,
            location="json",
        )

        try:
            content = YoutubeTranscriptManager.get_transcript(
                video_id, lang_code, format
            )

            if not content:
                abort(404, message="Subtitle not found")

            filename = f"{video_id}_{lang_code}.{format}"
            return create_download_response(content.encode("utf-8"), filename)

        except ValueError as e:
            abort(400, message=str(e))
        except Exception as e:
            logger.exception(f"Failed to download subtitle: {str(e)}")
            abort(500, message=str(e))
