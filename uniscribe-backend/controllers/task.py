import logging
from datetime import datetime

from flask import current_app
from flask_restful import abort
from sqlalchemy import insert

from constants.storage import EXPIRE_FOR_READ_URL
from constants.task import TaskStatus, TaskType
from constants.transcription import TranscriptionFileStatus, TranscriptionFileSourceType
from constants.language import LANGUAGES
from controllers.usage import check_transcription_quota
from exceptions.transcription import InsufficientTranscriptionQuotaError
from libs.id_generator import id_generator
from models import db_transaction, insert_record, insert_records
from models.task import Task
from models.task_result import TaskResult
from models.transcription_file import TranscriptionFile
from models.user import User
from models import db
from services.entitlement_service import EntitlementService

logger = logging.getLogger(__name__)


@db_transaction()
def create_transcription_task(user_id, transcription_file_id):
    """创建转录任务"""
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(403, message="User not allowed to create_transcription_task this file")

    # 生成任务
    task_id = id_generator.get_id()

    status = TaskStatus.pending.id
    error_message = ""
    try:
        check_transcription_quota(user_id, transcription_file.duration)
    except InsufficientTranscriptionQuotaError as e:
        status = TaskStatus.failed.id
        error_message = e.description

    # 使用模型选择服务确定合适的模型
    from services.model_selection_service import ModelSelectionService

    selected_model = ModelSelectionService.select_transcription_model(
        user_id, transcription_file
    )

    values = {
        "id": task_id,
        "file_id": transcription_file_id,
        "status": status,
        "task_type": TaskType.transcription.id,
        "error_message": error_message,
        "transcription_type": transcription_file.transcription_type,
        "requested_service_provider": selected_model,
    }

    stmt = insert(Task).prefix_with("IGNORE").values(**values)
    db.session.execute(stmt)
    db.session.flush()

    # 无论是新插入还是已存在，都获取任务
    return Task.get_by_file_id(file_id=transcription_file_id)


@db_transaction()
def create_text_tasks(transcription_file_id):
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    user = User.get_by_id(transcription_file.user_id)
    if not user:
        abort(404, message="User not found")

    task_types = [
        # TaskType.correction.id,  // 暂时不需要了
        TaskType.summary.id,
        # TaskType.keywords.id,  暂时不需要了，代码保留
        TaskType.outline.id,
        TaskType.qa_extraction.id,
    ]

    tasks = []
    for task_type in task_types:
        task = Task(
            id=id_generator.get_id(),
            file_id=transcription_file_id,
            status=TaskStatus.pending.id,
            task_type=task_type,
        )
        tasks.append(task)
    task_ids = insert_records(tasks)
    return Task.get_by_ids(task_ids)


@db_transaction()
def get_next_task():
    next_task = Task.get_next_task()
    if not next_task:
        abort(404, message="No task found")

    # 修改任务状态
    next_task.status = TaskStatus.processing.id
    next_task.started_time = datetime.now()

    # get file_url
    file = TranscriptionFile.get_by_id(next_task.file_id)
    if not file:
        db.session.delete(next_task)
        db.session.commit()
        logger.error(f"Deleted task {next_task.id} due to missing file")
        abort(404, message=f"Transcription file not found: {next_task.file_id}")
    # file_url 转成可以公开读取的 url
    storage = current_app.storage
    file_url = storage.generate_presigned_url_for_read(
        file.file_key,
        EXPIRE_FOR_READ_URL,
    )
    next_task.file_url = file_url
    next_task.file_duration = file.duration
    next_task.language_code = file.language_code
    next_task.language = file.language
    return next_task


@db_transaction()
def update_file_status(file_id):
    file = TranscriptionFile.get_by_id(file_id)
    if not file:
        abort(404, message="Transcription file not found")

    tasks = Task.get_all_by_file_id(file.id)

    # 检查是否存在所有必要的任务类型
    task_types = [task.task_type for task in tasks]
    expected_task_types = [
        TaskType.transcription.id,
        TaskType.summary.id,
        TaskType.outline.id,
        TaskType.qa_extraction.id,
    ]
    # 如果是YouTube来源，还应该有YouTube下载任务
    if file.source_type == TranscriptionFileSourceType.YOUTUBE:
        expected_task_types.append(TaskType.youtube_download.id)

    # 检查是否所有预期的任务类型都存在
    all_expected_tasks_exist = all(
        task_type in task_types for task_type in expected_task_types
    )

    if all(task.status == TaskStatus.pending.id for task in tasks):
        # 所有任务都处于 pending 状态
        file.status = TranscriptionFileStatus.uploaded.id
    elif any(
        task.task_type == TaskType.transcription.id
        and task.status == TaskStatus.completed.id
        for task in tasks
    ) and not critical_task_failed(tasks):
        # 检查是否所有任务都已处于最终状态（完成或失败）
        all_tasks_finalized = all(
            task.status in [TaskStatus.completed.id, TaskStatus.failed.id]
            for task in tasks
        )

        if all_tasks_finalized:
            # 所有任务都已处于最终状态
            if (
                all(task.status == TaskStatus.completed.id for task in tasks)
                and all_expected_tasks_exist
            ):
                # 所有预期的任务类型都存在且都已成功完成
                file.status = TranscriptionFileStatus.completed.id
            elif any(task.status == TaskStatus.failed.id for task in tasks):
                # 部分任务失败，但转录任务成功完成
                file.status = TranscriptionFileStatus.completed_with_errors.id
            else:
                # 所有现有任务都已成功完成，但缺少某些预期的任务类型
                # 这种情况通常是转录任务刚完成，其他文本任务还没创建
                file.status = TranscriptionFileStatus.processing.id
        else:
            # 部分任务仍在处理中或待处理
            file.status = TranscriptionFileStatus.partially_completed.id
    elif any(task.status == TaskStatus.processing.id for task in tasks):
        # 有任务正在处理中
        file.status = TranscriptionFileStatus.processing.id
    elif all(
        task.status == TaskStatus.failed.id for task in tasks
    ) or critical_task_failed(tasks):
        # 所有任务失败或关键任务失败
        file.status = TranscriptionFileStatus.failed.id
    else:
        # 此时是转录任务完成，等待创建文本任务的正常状态 (保持文件为 processing 状态)
        file.status = TranscriptionFileStatus.processing.id


def critical_task_failed(tasks):
    critical_task_types = [
        TaskType.transcription.id,
        TaskType.youtube_download.id,  # YouTube 下载也是关键任务
    ]
    return any(
        task.task_type in critical_task_types and task.status == TaskStatus.failed.id
        for task in tasks
    )


@db_transaction()
def create_youtube_download_task(user_id, transcription_file_id):
    """创建 YouTube 下载任务"""
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(
            403,
            message="User not allowed to create YouTube download task for this file",
        )

    # 生成任务
    task_id = id_generator.get_id()

    values = {
        "id": task_id,
        "file_id": transcription_file_id,
        "status": TaskStatus.processing.id,  # 直接设为处理中，因为会立即开始处理
        "task_type": TaskType.youtube_download.id,
        "error_message": "",
        "started_time": datetime.now(),
    }

    stmt = insert(Task).prefix_with("IGNORE").values(**values)
    db.session.execute(stmt)
    db.session.flush()

    # 无论是新插入还是已存在，都获取任务
    return Task.get_by_file_id_and_type(
        file_id=transcription_file_id, task_type=TaskType.youtube_download.id
    )


@db_transaction()
def save_task_result(
    task_id,
    task_type,
    duration=0,  # 废弃了
    language="",  # 废弃了
    original_text=None,
    corrected_text=None,
    segments=None,
    summary=None,
    keywords=None,
    outline=None,
    qa_extraction=None,
    error_message=None,
    detected_language=None,
    service_provider=None,  # 实际使用的服务提供商/模型
):
    task = Task.get_by_id(task_id)
    if not task:
        abort(404, message="Task not found")

    if task.task_type != task_type:
        abort(403, message="Task type mismatch")

    if error_message:
        task.error_message = error_message
        task.status = TaskStatus.failed.id
        return

    file = TranscriptionFile.get_by_id(task.file_id)
    if not file:
        abort(404, message="Transcription file not found")
    task.status = TaskStatus.completed.id
    task.completed_time = datetime.now()
    task.error_message = ""

    # 记录实际使用的模型
    if service_provider:
        task.actual_service_provider = service_provider

    if detected_language:
        detected_language = LANGUAGES.get(detected_language.lower(), None)

    if task_type == TaskType.transcription.id:
        # 允许多次提交
        result = TaskResult.get_by_file_id(file.id)
        if result:
            result.duration = duration
            result.language = language
            result.original_text = original_text
            result.segments = segments
            result.detected_language = detected_language
        else:
            result = TaskResult(
                id=id_generator.get_id(),
                file_id=file.id,
                duration=duration,
                language=language,
                original_text=original_text,
                segments=segments,
                detected_language=detected_language,
            )
            insert_record(result)

        # 更新用户权益使用量
        try:
            EntitlementService.update_usage(
                user_id=file.user_id, duration=file.duration, file_id=file.id
            )
        except Exception as e:
            logger.error(
                f"Failed to update entitlement for user {file.user_id}: {str(e)}"
            )
            raise

    elif task_type == TaskType.correction.id:
        result = TaskResult.get_by_file_id(file.id)
        result.corrected_text = corrected_text
    elif task_type == TaskType.summary.id:
        result = TaskResult.get_by_file_id(file.id)
        result.summary = summary
    elif task_type == TaskType.keywords.id:
        result = TaskResult.get_by_file_id(file.id)
        result.keywords = keywords
    elif task_type == TaskType.outline.id:
        result = TaskResult.get_by_file_id(file.id)
        result.outline = outline
    elif task_type == TaskType.qa_extraction.id:
        result = TaskResult.get_by_file_id(file.id)
        result.qa_extraction = qa_extraction
    return
