"""
CLI 命令定义模块

该模块定义并注册所有管理员级别的CLI命令。
"""

from . import admin_cli

# 导入用户管理命令
from scripts.add_transcription_minutes import add_minutes
from scripts.add_subscription import add_subscription
from scripts.deactivate_user import deactivate_user

# 导入文件存储管理命令
# from scripts.migrate_file_storage import migrate_file_storage

# 导入 AppSumo 相关命令
# from cli.migrations.add_ltd_plan_type import add_ltd_plan_type
# from cli.appsumo_test import appsumo_test

# 导入任务管理命令
# 注意：task_commands.py 中的命令通过装饰器自动注册，无需显式添加
import cli.task_commands  # noqa: F401

# 导入邮件测试命令
import cli.email_commands  # noqa: F401

# 注册用户管理命令
admin_cli.add_command(add_minutes)
admin_cli.add_command(add_subscription)
admin_cli.add_command(deactivate_user)
