import logging
from time import sleep

from services.queue_service import QueueService
from controllers.stripe import <PERSON>e<PERSON>ontroller
import stripe
from app import app

logger = logging.getLogger(__name__)


class StripeWebhookConsumer:
    def __init__(self):
        self.handlers = {
            "customer.subscription.created": StripeController.handle_subscription_created,
            "customer.subscription.updated": StripeController.handle_subscription_updated,
            "customer.subscription.deleted": StripeController.handle_subscription_expired,
            "checkout.session.completed": StripeController.handle_checkout_session_completed,
            "invoice.paid": StripeController.handle_invoice_paid,
        }

    def start(self):
        logger.info("Starting Stripe webhook consumer...")
        while True:
            try:
                # Dequeue and process events
                queue_data = QueueService.dequeue("stripe_events")
                if queue_data:
                    self.process_event(queue_data)
                else:
                    logger.info("No event data found in queue")
                    sleep(1)  # Avoid tight polling
            except Exception as e:
                logger.error("Error processing stripe event: %s", str(e), exc_info=True)
                # Consider adding retry logic here

    def process_event(self, queue_data):
        event_type = queue_data["event_type"]
        event_data = queue_data["event_data"]
        logger.info("Processing event type: %s, event data: %s", event_type, event_data)
        handler = self.handlers.get(event_type)
        if not handler:
            logger.warning("No handler found for event type: %s", event_type)
            return

        try:
            # Convert JSON event_data to Stripe Event object
            event = stripe.Event.construct_from(event_data, None)
            handler(event)
        except Exception as e:
            logger.error(
                "Error handling event type %s: %s",
                event_type,
                str(e),
                exc_info=True,
            )


if __name__ == "__main__":
    import os

    print(os.getenv("REDIS_HOST"))
    print(os.getenv("REDIS_PORT"))
    print(os.getenv("REDIS_PASSWORD"))
    # 初始化 consumer 并启动
    with app.app_context():
        consumer = StripeWebhookConsumer()
        consumer.start()
