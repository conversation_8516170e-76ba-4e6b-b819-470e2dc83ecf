import logging
import time
import os
import atexit
from flask import Flask, request, g
from bdb import BdbQuit
from flask_cors import CORS
from pyinstrument import Profiler
import sentry_sdk
from sentry_sdk.integrations.logging import LoggingIntegration
from yt_dlp.utils import ExtractorError, DownloadError
from exceptions.youtube import YoutubeExtractError

from libs.storage.factory import StorageFactory
from models import db
from resources import api, register_error_handlers
from routes import register_routes
from config import CONFIG, is_development_env, env, is_production_env
from libs.task_manager import TaskManager
from libs.logging import setup_logging
from cli import register_all_commands


if is_production_env():
    # 始终执行 Sentry 初始化
    def before_send(event, hint):
        # 使用 try-except 包装整个函数，避免任何错误影响应用
        try:
            # 处理异常事件
            if "exception" in event:
                exception = event["exception"]["values"][0]
                exc_type = exception.get("type", "")
                exc_value = exception.get("value", "")

                # 简单的类型匹配
                if exc_type in [
                    "BdbQuit",
                    "YoutubeExtractError",
                    "ExtractorError",
                    "DownloadError",
                ]:
                    logging.info(f"Sentry: Ignoring exception by type: {exc_type}")
                    return None

                # 检查异常消息
                if any(
                    keyword in exc_value
                    for keyword in [
                        "Private video",
                        "Sign in to confirm",
                        "Video unavailable",
                    ]
                ):
                    logging.info(
                        f"Sentry: Ignoring exception by message: {exc_value[:50]}..."
                    )
                    return None

            # 处理日志事件
            elif "logentry" in event:
                message = event.get("logentry", {}).get("message", "")

                # 检查日志消息
                if any(
                    keyword in message
                    for keyword in [
                        "Private video",
                        "Sign in to confirm",
                        "Video unavailable",
                        "ExtractorError",
                        "DownloadError",
                    ]
                ):
                    logging.info(f"Sentry: Ignoring log by message: {message[:50]}...")
                    return None

            # 检查面包屑中的日志
            breadcrumbs = event.get("breadcrumbs", {}).get("values", [])
            for crumb in breadcrumbs:
                if crumb.get("type") == "log" and crumb.get("level") == "error":
                    message = crumb.get("message", "")
                    if any(
                        keyword in message
                        for keyword in [
                            "Private video",
                            "Sign in to confirm",
                            "Video unavailable",
                            "ExtractorError",
                            "DownloadError",
                        ]
                    ):
                        print(
                            f"Sentry: Ignoring event with breadcrumb: {message[:50]}..."
                        )
                        return None
        except Exception as e:
            # 避免任何错误影响应用
            print(f"Error in before_send: {e}")

        return event

    sentry_sdk.init(
        dsn="https://<EMAIL>/4508829108404224",
        environment=env,
        traces_sample_rate=1.0,
        _experiments={
            "continuous_profiling_auto_start": True,
        },
        ignore_errors=[
            BdbQuit,
            YoutubeExtractError,
            ExtractorError,
            DownloadError,
        ],
        before_send=before_send,
        integrations=[
            LoggingIntegration(level=logging.ERROR, event_level=logging.ERROR)
        ],
    )


def create_app():
    """创建 Flask 应用"""
    app = Flask(__name__)

    app.config.from_object(CONFIG)

    # 初始化日志，保存 handler 引用
    axiom_handler = setup_logging(dataset_name=CONFIG.AXIOM.get("dataset_name"))
    logger = logging.getLogger(__name__)
    logger.info("Application started")

    # 注册退出处理
    def handle_exit():
        logger.info("Application shutting down, flushing remaining logs...")
        axiom_handler.client.flush()

    atexit.register(handle_exit)

    # CORS 设置
    if is_development_env():
        CORS(app, supports_credentials=True, allow_headers="*", origins="*")
    else:
        CORS(
            app,
            origins=[
                "https://shiyin-web.vercel.app",
                "https://www.uniscribe.co",
                "https://uniscribe.co",
                r"^https:\/\/uniscribe-.*\.vercel\.app$",
            ],
            supports_credentials=True,
        )

    # 初始化各种组件
    db.init_app(app)
    register_routes(api)
    register_error_handlers()
    api.init_app(app)

    # 注册 CLI 命令
    register_all_commands(app)

    # 初始化 storage
    storage_type = app.config.get("STORAGE_TYPE")
    if storage_type == "s3":
        storage_config = app.config.get("CLOUDFLARE_R2")
    elif storage_type == "cos":
        storage_config = app.config.get("TENCENT_CLOUD")
    else:
        raise ValueError(f"Unsupported storage type: {storage_type}")

    app.storage = StorageFactory.get_storage(storage_type, storage_config)
    app.task_manager = TaskManager()

    with app.app_context():
        db.create_all()

    # 禁用 werkzeug 的默认日志
    logging.getLogger("werkzeug").disabled = True

    # 请求钩子
    @app.before_request
    def before_request():
        request.start_time = time.time()
        if is_development_env() and "profile" in request.args:
            g.profiler = Profiler()
            g.profiler.start()

    @app.after_request
    def after_request(response):
        end_time = time.time()
        elapsed_time = end_time - request.start_time
        user_id = getattr(g.user, "id", None) if hasattr(g, "user") else None
        # 记录请求日志
        logger.info(
            f"{request.method} {request.path}",
            extra={
                "ip": request.remote_addr,
                "method": request.method,
                "path": request.path,
                "url": request.url,
                "user_id": str(user_id) if user_id else None,
                "status_code": response.status_code,
                "elapsed_time_ms": round(elapsed_time * 1000, 2),
                "user_agent": request.headers.get("User-Agent"),
                "referer": request.headers.get("Referer"),
                "query_string": request.query_string.decode("utf-8"),
            },
        )

        if not hasattr(g, "profiler"):
            return response
        g.profiler.stop()
        g.profiler.open_in_browser()

        return response

    if is_development_env():
        print_app_config(app)

    return app


def print_app_config(app):
    print(f"current env: {os.getenv('APP_SETTINGS')}")
    if not is_development_env():
        print("Not in development environment, skipping config print")
        return
    print("Flask App Configuration:")
    for key in app.config:
        print(f"{key} = {app.config[key]}")


# 创建应用实例
app = create_app()

if __name__ == "__main__":
    import sys

    port = int(sys.argv[1]) if len(sys.argv) == 2 else 8000
    app.run(host="0.0.0.0", port=port, debug=CONFIG.DEBUG)
